<template>
  <div class="smart-detection-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        {{ t('howItWorks.steps.detection.title') }}
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {{ t('howItWorks.steps.detection.description') }}
      </p>
    </div>

    <!-- 表单检测动画 -->
    <div class="relative max-w-2xl mx-auto">
      <!-- 表单容器 -->
      <div class="form-container relative bg-white rounded-xl shadow-lg border-2 border-blue-100 p-8 overflow-hidden">
        
        <!-- 表单内容 -->
        <div class="space-y-6">
          <!-- 表单标题 -->
          <h4 class="text-xl font-semibold text-gray-800 mb-6">Contact Us</h4>
          
          <!-- 表单字段 -->
          <div class="space-y-5">
            <!-- 姓名字段 -->
            <div class="field-group animate-field-detection" style="animation-delay: 0.5s;">
              <label class="block text-base font-medium text-gray-700 mb-2">Full Name</label>
              <div class="relative">
                <input 
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                  placeholder="Enter your full name"
                />
                <!-- 围绕文本框的跑马灯 -->
                <div class="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-0 animate-border-scan" style="animation-delay: 0.5s; box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);"></div>
              </div>
            </div>

            <!-- 邮箱字段 -->
            <div class="field-group animate-field-detection" style="animation-delay: 1s;">
              <label class="block text-base font-medium text-gray-700 mb-2">Email Address</label>
              <div class="relative">
                <input 
                  type="email" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                  placeholder="Enter your email address"
                />
                <!-- 围绕文本框的跑马灯 -->
                <div class="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-0 animate-border-scan" style="animation-delay: 1s; box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);"></div>
              </div>
            </div>

            <!-- 公司字段 -->
            <div class="field-group animate-field-detection" style="animation-delay: 1.5s;">
              <label class="block text-base font-medium text-gray-700 mb-2">Company</label>
              <div class="relative">
                <input 
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                  placeholder="Enter your company name"
                />
                <!-- 围绕文本框的跑马灯 -->
                <div class="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-0 animate-border-scan" style="animation-delay: 1.5s; box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);"></div>
              </div>
            </div>

            <!-- 消息字段 -->
            <div class="field-group animate-field-detection" style="animation-delay: 2s;">
              <label class="block text-base font-medium text-gray-700 mb-2">Message</label>
              <div class="relative">
                <textarea 
                  rows="4" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base resize-none"
                  placeholder="Tell us about your project or inquiry"
                ></textarea>
                <!-- 围绕文本框的跑马灯 -->
                <div class="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-0 animate-border-scan" style="animation-delay: 2s; box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);"></div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="field-group animate-field-detection" style="animation-delay: 2.5s;">
              <button class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 font-medium text-base">
                Send Message
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
</script>

<style scoped>
/* 表单检测动画 */
@keyframes field-detection {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes border-scan {
  0% {
    opacity: 0;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 15px 2px rgba(59, 130, 246, 0.6);
  }
  100% {
    opacity: 0;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.animate-field-detection {
  animation: field-detection 0.8s ease-out forwards;
}

.animate-border-scan {
  animation: border-scan 3s ease-in-out infinite;
}

.field-group {
  opacity: 0;
}

/* 增强表单样式 */
.form-container {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
}

/* 输入框增强样式 */
input, textarea {
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 按钮增强样式 */
button {
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
  transform: translateY(0);
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
}

button:active {
  transform: translateY(0);
}



/* 响应式优化 */
@media (max-width: 768px) {
  .form-container {
    padding: 1.5rem;
  }
}
</style>