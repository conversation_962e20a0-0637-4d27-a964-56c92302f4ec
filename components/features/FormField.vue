<template>
  <div class="form-field-wrapper">
    <label v-if="label" class="block text-sm font-medium text-gray-700 mb-2">{{ label }}</label>
    <div class="relative">
      <component 
        :is="isTextarea ? 'textarea' : 'input'"
        :type="type"
        :placeholder="isTyping || filledText ? '' : placeholder"
        :rows="isTextarea ? (rows || 4) : undefined"
        class="w-full px-0 py-2 border-0 border-b-2 border-gray-300 focus:outline-none focus:border-blue-500 transition-all duration-200 bg-transparent"
        :class="{ 
          'resize-none': isTextarea,
          'pb-2': !isLineStyle
        }"
        readonly
      />
      
      <!-- 打字机效果的文本 -->
      <div 
        class="absolute inset-0 px-0 py-2 pointer-events-none"
        :class="{ 
          'text-gray-900': filledText, 
          'text-transparent': !filledText,
          'pb-2': !isLineStyle
        }"
      >
        <span 
          ref="typingText"
          class="typewriter-text"
          :style="{ animationDelay: `${delay}s` }"
        >{{ filledText }}</span>
        <span 
          v-if="showCursor && isTyping" 
          class="typing-cursor animate-blink"
        >|</span>
      </div>

      <!-- 填充进度指示器 -->
      <div 
        class="absolute border-0 border-b-2 border-green-500 opacity-0 transition-opacity duration-300"
        :class="{ 'opacity-50': isTyping, 'textarea-indicator': isTextarea, 'input-indicator': !isTextarea }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

interface Props {
  label: string
  value: string
  delay: number
  placeholder?: string
  type?: string
  isTextarea?: boolean
  isLineStyle?: boolean
  rows?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  type: 'text',
  isTextarea: false,
  isLineStyle: false,
  rows: 4
})

const filledText = ref('')
const isTyping = ref(false)
const showCursor = ref(false)
const typingText = ref<HTMLElement>()

let typingTimer: NodeJS.Timeout

const startTyping = () => {
  isTyping.value = true
  showCursor.value = true
  filledText.value = ''
  
  let currentIndex = 0
  const text = props.value
  
  const typeChar = () => {
    if (currentIndex < text.length) {
      filledText.value = text.substring(0, currentIndex + 1)
      currentIndex++
      
      // 根据字符类型调整打字速度
      const char = text[currentIndex - 1]
      let speed = 80 // 基础速度
      
      if (char === ' ') speed = 50 // 空格快一些
      else if (char === '\n') speed = 200 // 换行慢一些
      else if (/[,.!?;:]/.test(char)) speed = 150 // 标点符号慢一些
      
      typingTimer = setTimeout(typeChar, speed)
    } else {
      isTyping.value = false
      showCursor.value = false
    }
  }
  
  typeChar()
}

onMounted(() => {
  const timer = setTimeout(startTyping, props.delay * 1000)
  
  return () => {
    clearTimeout(timer)
    if (typingTimer) clearTimeout(typingTimer)
  }
})

// 监听 value 变化，重新开始打字动画
watch(() => props.value, () => {
  if (typingTimer) clearTimeout(typingTimer)
  const timer = setTimeout(startTyping, props.delay * 1000)
  
  return () => clearTimeout(timer)
})
</script>

<style scoped>
.typewriter-text {
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
}

.typing-cursor {
  display: inline-block;
  margin-left: 1px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

.form-field-wrapper {
  opacity: 0;
  animation: fade-in 0.5s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-indicator {
  inset: 0;
}

.textarea-indicator {
  left: 0;
  right: 0;
  height: 2px;
  bottom: 8px;
}
</style>