<template>
  <div class="user-prompt-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Describe Your Needs
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Simply tell Fillify what you want to fill in the form with a natural language description
      </p>
    </div>

    <!-- 用户输入动画 -->
    <div class="relative max-w-3xl mx-auto">
      <!-- 表单容器 -->
      <div class="form-container relative bg-white rounded-xl shadow-lg border-2 border-yellow-100 p-8 overflow-hidden">
        <!-- 表单内容 -->
        <div class="space-y-6">
          <!-- 表单标题 -->
          <h4 class="text-xl font-semibold text-gray-800 mb-6">Contact Us</h4>
          
          <!-- 表单字段 -->
          <div class="space-y-5">
            <!-- 姓名字段 -->
            <div class="field-group">
              <label class="block text-base font-medium text-gray-700 mb-2">Full Name</label>
              <div class="relative">
                <input 
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-base bg-gray-50"
                  placeholder="Enter your full name"
                  readonly
                />
              </div>
            </div>

            <!-- 邮箱字段 -->
            <div class="field-group">
              <label class="block text-base font-medium text-gray-700 mb-2">Email Address</label>
              <div class="relative">
                <input 
                  type="email" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-base bg-gray-50"
                  placeholder="Enter your email address"
                  readonly
                />
              </div>
            </div>

            <!-- 公司字段 -->
            <div class="field-group">
              <label class="block text-base font-medium text-gray-700 mb-2">Company</label>
              <div class="relative">
                <input 
                  type="text" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-base bg-gray-50"
                  placeholder="Enter your company name"
                  readonly
                />
              </div>
            </div>

            <!-- 消息字段 -->
            <div class="field-group">
              <label class="block text-base font-medium text-gray-700 mb-2">Message</label>
              <div class="relative">
                <textarea 
                  rows="4" 
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-base resize-none bg-gray-50"
                  placeholder="Tell us about your project or inquiry"
                  readonly
                ></textarea>
              </div>
            </div>

            <!-- 用户输入描述区域 -->
            <div class="field-group mt-8">
              <label class="block text-base font-medium text-gray-700 mb-2">Describe what you want to fill</label>
              <div class="relative">
                <div class="w-full px-4 py-3 border-2 border-yellow-400 rounded-lg bg-yellow-50 min-h-[120px]">
                  <div class="typewriter-container">
                    <span 
                      ref="typewriterText"
                      class="text-gray-800"
                    >{{ displayedText }}</span>
                    <span 
                      v-if="isTyping" 
                      class="typing-cursor animate-blink"
                    >|</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="field-group">
              <button class="w-full bg-gradient-to-r from-yellow-500 to-amber-500 text-white py-3 px-6 rounded-lg hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 font-medium text-base">
                Generate Form Content
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const displayedText = ref('')
const isTyping = ref(true)
const typewriterText = ref<HTMLElement>()

const fullText = "Fill this form with my contact info: John Doe, <EMAIL>, working at TechCorp as a software engineer. Add a message about inquiring about your AI form filling service."

let typingTimer: NodeJS.Timeout

const startTyping = () => {
  let currentIndex = 0
  
  const typeChar = () => {
    if (currentIndex < fullText.length) {
      displayedText.value = fullText.substring(0, currentIndex + 1)
      currentIndex++
      
      // 根据字符类型调整打字速度
      const char = fullText[currentIndex - 1]
      let speed = 80 // 基础速度
      
      if (char === ' ') speed = 50 // 空格快一些
      else if (char === ',') speed = 150 // 逗号慢一些
      else if (char === '.') speed = 200 // 句号慢一些
      
      typingTimer = setTimeout(typeChar, speed)
    } else {
      isTyping.value = false
      
      // 2秒后重新开始打字动画
      setTimeout(() => {
        isTyping.value = true
        displayedText.value = ''
        currentIndex = 0
        typeChar()
      }, 2000)
    }
  }
  
  typeChar()
}

onMounted(() => {
  const timer = setTimeout(startTyping, 500)
  
  return () => {
    clearTimeout(timer)
    if (typingTimer) clearTimeout(typingTimer)
  }
})
</script>

<style scoped>
.typewriter-container {
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.typing-cursor {
  display: inline-block;
  margin-left: 1px;
  color: #ca8a04; /* yellow-600 */
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

.user-prompt-container {
  opacity: 0;
  animation: fade-in 0.5s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单样式 */
.form-container {
  box-shadow: 0 10px 25px -5px rgba(251, 191, 36, 0.1), 0 8px 10px -6px rgba(251, 191, 36, 0.1);
}

input, textarea {
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.15);
}

button {
  box-shadow: 0 4px 14px 0 rgba(251, 191, 36, 0.3);
  transform: translateY(0);
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(251, 191, 36, 0.4);
}

button:active {
  transform: translateY(0);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-container {
    padding: 1.5rem;
  }
}
</style>