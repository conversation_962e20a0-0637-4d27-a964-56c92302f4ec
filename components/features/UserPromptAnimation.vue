<template>
  <div class="user-prompt-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Describe Your Needs
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Simply tell Fillify what you want to fill in the form with a natural language description
      </p>
    </div>

    <!-- 主要内容区域 -->
    <div class="relative max-w-4xl mx-auto">
      <div class="grid md:grid-cols-2 gap-8 items-center">

        <!-- 左侧：Fillify 插件界面模拟 -->
        <div class="order-2 md:order-1">
          <div class="fillify-popup bg-white rounded-xl shadow-2xl border border-gray-200 p-6 max-w-md mx-auto">
            <!-- 插件头部 -->
            <div class="flex items-center gap-3 mb-6 pb-4 border-b border-gray-100">
              <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Fillify</h4>
                <p class="text-xs text-gray-500">AI Form Assistant</p>
              </div>
            </div>

            <!-- 检测到的表单提示 -->
            <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-blue-800">Form Detected</span>
              </div>
              <p class="text-xs text-blue-600">Contact form with 4 fields found</p>
            </div>

            <!-- 用户输入区域 -->
            <div class="space-y-4">
              <label class="block text-sm font-medium text-gray-700">
                What would you like to fill in this form?
              </label>

              <!-- 输入框 -->
              <div class="relative">
                <div class="w-full px-4 py-3 border-2 border-yellow-400 rounded-lg bg-yellow-50 min-h-[120px] focus-within:border-yellow-500 transition-colors">
                  <div class="typewriter-container">
                    <span class="text-gray-800 text-sm leading-relaxed">{{ displayedText }}</span>
                    <span
                      v-if="isTyping"
                      class="typing-cursor animate-blink text-yellow-600"
                    >|</span>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex gap-2">
                <button
                  class="flex-1 bg-gradient-to-r from-yellow-500 to-amber-500 text-white py-2.5 px-4 rounded-lg hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 font-medium text-sm"
                  :class="{ 'opacity-75 cursor-not-allowed': isTyping }"
                >
                  <span v-if="!isTyping">Fill Form</span>
                  <span v-else class="flex items-center justify-center gap-2">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Processing...
                  </span>
                </button>
                <button class="px-4 py-2.5 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm text-gray-600">
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：说明文字和特性 -->
        <div class="order-1 md:order-2 space-y-6">
          <div class="space-y-4">
            <h4 class="text-2xl font-bold text-gray-900">Natural Language Input</h4>
            <p class="text-gray-600 leading-relaxed">
              No need to fill each field manually. Just describe what information you want to use, and Fillify's AI will understand and map it to the correct form fields.
            </p>
          </div>

          <!-- 特性列表 -->
          <div class="space-y-3">
            <div class="flex items-start gap-3">
              <div class="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div>
                <p class="font-medium text-gray-900">Smart Understanding</p>
                <p class="text-sm text-gray-600">AI interprets your natural language description</p>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div>
                <p class="font-medium text-gray-900">Context Aware</p>
                <p class="text-sm text-gray-600">Understands relationships between different pieces of information</p>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5">
                <svg class="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div>
                <p class="font-medium text-gray-900">Flexible Input</p>
                <p class="text-sm text-gray-600">Works with any writing style or format</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const displayedText = ref('')
const isTyping = ref(true)

const fullText = "Fill this form with my contact info: John Doe, <EMAIL>, working at TechCorp as a software engineer. Add a message about inquiring about your AI form filling service."

let typingTimer: NodeJS.Timeout
let restartTimer: NodeJS.Timeout

const startTyping = () => {
  let currentIndex = 0

  const typeChar = () => {
    if (currentIndex < fullText.length) {
      displayedText.value = fullText.substring(0, currentIndex + 1)
      currentIndex++

      // 根据字符类型调整打字速度
      const char = fullText[currentIndex - 1]
      let speed = 80 // 基础速度

      if (char === ' ') speed = 50 // 空格快一些
      else if (char === ',') speed = 150 // 逗号慢一些
      else if (char === '.') speed = 200 // 句号慢一些

      typingTimer = setTimeout(typeChar, speed)
    } else {
      isTyping.value = false

      // 3秒后重新开始打字动画
      restartTimer = setTimeout(() => {
        isTyping.value = true
        displayedText.value = ''
        currentIndex = 0
        typeChar()
      }, 3000)
    }
  }

  typeChar()
}

onMounted(() => {
  const timer = setTimeout(startTyping, 800)

  return () => {
    clearTimeout(timer)
  }
})

onUnmounted(() => {
  if (typingTimer) clearTimeout(typingTimer)
  if (restartTimer) clearTimeout(restartTimer)
})
</script>

<style scoped>
.typewriter-container {
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.typing-cursor {
  display: inline-block;
  margin-left: 1px;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

.user-prompt-container {
  opacity: 0;
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fillify 插件样式 */
.fillify-popup {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(251, 191, 36, 0.1);
  transition: all 0.3s ease;
}

.fillify-popup:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(251, 191, 36, 0.2);
}

/* 按钮样式 */
button {
  transition: all 0.3s ease;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse-dot 2s infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .fillify-popup {
    margin: 0 1rem;
  }

  .grid {
    gap: 2rem;
  }
}

/* 加载动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>