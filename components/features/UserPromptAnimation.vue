<template>
  <div class="user-prompt-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Describe Your Needs
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Simply tell Fillify what you want to fill in the form with a natural language description
      </p>
    </div>

    <!-- 用户输入动画 -->
    <div class="relative max-w-2xl mx-auto">
      <!-- 输入框容器 -->
      <div class="prompt-container relative bg-white rounded-xl shadow-lg border-2 border-yellow-100 p-8 overflow-hidden">

        <!-- 输入框标题 -->
        <div class="mb-6">
          <h4 class="text-xl font-semibold text-gray-800 mb-2">Tell Fillify what to fill</h4>
          <p class="text-sm text-gray-600">Describe the information you want to use in natural language</p>
        </div>

        <!-- 主要输入区域 -->
        <div class="space-y-4">
          <!-- 用户输入文本框 -->
          <div class="relative">
            <div class="w-full px-4 py-4 border-2 border-yellow-400 rounded-lg bg-yellow-50 min-h-[140px] relative overflow-hidden">
              <!-- 打字动画容器 -->
              <div class="typewriter-container">
                <span class="text-gray-800 text-base leading-relaxed">{{ displayedText }}</span>
                <span
                  v-if="isTyping"
                  class="typing-cursor animate-blink text-yellow-600"
                >|</span>
              </div>

              <!-- 输入框光效动画 -->
              <div class="absolute inset-0 border-2 border-yellow-500 rounded-lg opacity-0 animate-glow-pulse"></div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3">
            <button
              class="flex-1 bg-gradient-to-r from-yellow-500 to-amber-500 text-white py-3 px-6 rounded-lg hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 font-medium"
              :class="{ 'opacity-75 cursor-not-allowed': isTyping }"
            >
              <span v-if="!isTyping">Generate & Fill</span>
              <span v-else class="flex items-center justify-center gap-2">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Processing...
              </span>
            </button>
          </div>
        </div>

        <!-- 背景装饰 -->
        <div class="absolute top-4 right-4 w-16 h-16 bg-yellow-100 rounded-full opacity-20"></div>
        <div class="absolute bottom-4 left-4 w-12 h-12 bg-amber-100 rounded-full opacity-30"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const displayedText = ref('')
const isTyping = ref(true)

const fullText = "Fill this form with my contact info: John Doe, <EMAIL>, working at TechCorp as a software engineer. Add a message about inquiring about your AI form filling service."

let typingTimer: NodeJS.Timeout
let restartTimer: NodeJS.Timeout

const startTyping = () => {
  let currentIndex = 0

  const typeChar = () => {
    if (currentIndex < fullText.length) {
      displayedText.value = fullText.substring(0, currentIndex + 1)
      currentIndex++

      // 根据字符类型调整打字速度
      const char = fullText[currentIndex - 1]
      let speed = 80 // 基础速度

      if (char === ' ') speed = 50 // 空格快一些
      else if (char === ',') speed = 150 // 逗号慢一些
      else if (char === '.') speed = 200 // 句号慢一些

      typingTimer = setTimeout(typeChar, speed)
    } else {
      isTyping.value = false

      // 3秒后重新开始打字动画
      restartTimer = setTimeout(() => {
        isTyping.value = true
        displayedText.value = ''
        currentIndex = 0
        typeChar()
      }, 3000)
    }
  }

  typeChar()
}

onMounted(() => {
  const timer = setTimeout(startTyping, 800)

  return () => {
    clearTimeout(timer)
  }
})

onUnmounted(() => {
  if (typingTimer) clearTimeout(typingTimer)
  if (restartTimer) clearTimeout(restartTimer)
})
</script>

<style scoped>
.typewriter-container {
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.typing-cursor {
  display: inline-block;
  margin-left: 1px;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

.user-prompt-container {
  opacity: 0;
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框容器样式 */
.prompt-container {
  box-shadow: 0 10px 25px -5px rgba(251, 191, 36, 0.1), 0 8px 10px -6px rgba(251, 191, 36, 0.1);
  transition: all 0.3s ease;
}

.prompt-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(251, 191, 36, 0.15);
}

/* 光效动画 */
@keyframes glow-pulse {
  0% {
    opacity: 0;
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(251, 191, 36, 0.2);
  }
  100% {
    opacity: 0;
    box-shadow: 0 0 0 8px rgba(251, 191, 36, 0);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 2s infinite;
}

/* 按钮样式 */
button {
  box-shadow: 0 4px 14px 0 rgba(251, 191, 36, 0.3);
  transform: translateY(0);
  transition: all 0.3s ease;
}

button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(251, 191, 36, 0.4);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* 加载动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .prompt-container {
    padding: 1.5rem;
    margin: 0 1rem;
  }
}
</style>