<template>
  <div class="ai-analysis-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        {{ t('howItWorks.steps.analysis.title') }}
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {{ t('howItWorks.steps.analysis.description') }}
      </p>
    </div>

    <!-- AI 分析动画 -->
    <div class="relative flex items-center justify-center h-96">
      <!-- 中央核心和轨道容器 -->
      <div class="central-core relative flex items-center justify-center" style="width: 400px; height: 400px;">
        <!-- 主核心 -->
        <div class="w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full shadow-xl flex items-center justify-center animate-core-pulse z-10">
          <!-- AI 机器人图标 -->
          <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5L1 7V9C1 10.1 1.9 11 3 11V19C3 20.1 3.9 21 5 21H15C16.1 21 17 20.1 17 19V11C18.1 11 19 10.1 19 9H21ZM15 19H5V11H15V19ZM12 12C10.34 12 9 13.34 9 15C9 16.66 10.34 18 12 18C13.66 18 15 16.66 15 15C15 13.34 13.66 12 12 12ZM7 15C7 14.21 7.23 13.47 7.62 12.83L6.5 11.71C5.95 12.5 5.62 13.43 5.62 14.42L7 15.81V15H7C7 15.27 7 15.53 7 15.81V15ZM17 15C17 15.81 16.77 16.56 16.38 17.21L17.5 18.33C18.05 17.54 18.38 16.61 18.38 15.62L17 14.23V15H17C17 15.27 17 15.53 17 15.81V15Z"/>
          </svg>
        </div>

        <!-- 能量波纹 -->
        <div class="absolute inset-0 rounded-full animate-ripple-1"></div>
        <div class="absolute inset-0 rounded-full animate-ripple-2"></div>
        <div class="absolute inset-0 rounded-full animate-ripple-3"></div>
        
        <!-- 轨道和 AI 提供商图标 -->
        <div class="orbit-container absolute inset-0">
          <!-- 外层轨道 -->
          <div class="orbit-ring orbit-ring-outer animate-orbit-clockwise">
            <!-- OpenAI -->
            <div class="orbit-icon orbit-position-1">
              <div class="icon-wrapper bg-white">
                <img src="/icon/openai.png" alt="OpenAI" class="w-full h-full object-contain" />
              </div>
            </div>
            
            <!-- Claude -->
            <div class="orbit-icon orbit-position-2">
              <div class="icon-wrapper bg-white">
                <img src="/icon/claude.png" alt="Claude" class="w-full h-full object-contain" />
              </div>
            </div>
            
            <!-- Gemini -->
            <div class="orbit-icon orbit-position-3">
              <div class="icon-wrapper bg-white">
                <img src="/icon/gemini.png" alt="Gemini" class="w-full h-full object-contain" />
              </div>
            </div>
          </div>

          <!-- 内层轨道 -->
          <div class="orbit-ring orbit-ring-inner animate-orbit-counterclockwise">
            <!-- Ollama -->
            <div class="orbit-icon orbit-position-1">
              <div class="icon-wrapper bg-white">
                <img src="/icon/ollama.png" alt="Ollama" class="w-full h-full object-contain" />
              </div>
            </div>
            
            <!-- DeepSeek -->
            <div class="orbit-icon orbit-position-2">
              <div class="icon-wrapper bg-white">
                <img src="/icon/deepseek.png" alt="DeepSeek" class="w-full h-full object-contain" />
              </div>
            </div>
            
            <!-- Moonshot -->
            <div class="orbit-icon orbit-position-3">
              <div class="icon-wrapper bg-white">
                <img src="/icon/moonshot.png" alt="Moonshot" class="w-full h-full object-contain" />
              </div>
            </div>

            <!-- OpenRouter -->
            <div class="orbit-icon orbit-position-4">
              <div class="icon-wrapper bg-white">
                <img src="/icon/openrouter.png" alt="OpenRouter" class="w-full h-full object-contain" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
</script>

<style scoped>
/* 中央核心动画 */
@keyframes core-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 40px rgba(147, 51, 234, 0.5);
  }
}

.animate-core-pulse {
  animation: core-pulse 2s ease-in-out infinite;
}

/* 能量波纹动画 */
@keyframes ripple-1 {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes ripple-2 {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

@keyframes ripple-3 {
  0% {
    transform: scale(1);
    opacity: 0.2;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

.animate-ripple-1 {
  animation: ripple-1 2s ease-out infinite;
  border: 2px solid rgb(147, 51, 234);
}

.animate-ripple-2 {
  animation: ripple-2 2s ease-out infinite 0.7s;
  border: 2px solid rgb(59, 130, 246);
}

.animate-ripple-3 {
  animation: ripple-3 2s ease-out infinite 1.4s;
  border: 2px solid rgb(168, 85, 247);
}

/* 轨道动画 */
.orbit-container {
  width: 400px;
  height: 400px;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orbit-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px dashed rgba(147, 51, 234, 0.2);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.orbit-ring-outer {
  width: 300px;
  height: 300px;
  margin-top: -150px;
  margin-left: -150px;
}

.orbit-ring-inner {
  width: 200px;
  height: 200px;
  margin-top: -100px;
  margin-left: -100px;
}

@keyframes orbit-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes orbit-counterclockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.animate-orbit-clockwise {
  animation: orbit-clockwise 12s linear infinite;
}

.animate-orbit-counterclockwise {
  animation: orbit-counterclockwise 8s linear infinite;
}

/* 图标位置 */
.orbit-icon {
  position: absolute;
  width: 40px;
  height: 40px;
}

.orbit-position-1 {
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.orbit-position-2 {
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
}

.orbit-position-3 {
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.orbit-position-4 {
  top: 50%;
  left: -20px;
  transform: translateY(-50%);
}

.icon-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: icon-counter-rotate 12s linear infinite reverse;
}

.orbit-ring-inner .icon-wrapper {
  animation: icon-counter-rotate 8s linear infinite;
}

@keyframes icon-counter-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 数据流动画 */
.data-stream {
  position: absolute;
  width: 2px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, rgba(147, 51, 234, 0.6), transparent);
  border-radius: 1px;
}

.data-stream-1 {
  top: 20%;
  left: 70%;
  transform: rotate(45deg);
}

.data-stream-2 {
  top: 60%;
  right: 20%;
  transform: rotate(-30deg);
}

.data-stream-3 {
  bottom: 30%;
  left: 25%;
  transform: rotate(60deg);
}

.data-stream-4 {
  top: 40%;
  left: 15%;
  transform: rotate(-45deg);
}

@keyframes data-flow-1 {
  0%, 100% {
    opacity: 0;
    transform: rotate(45deg) translateY(-20px);
  }
  50% {
    opacity: 1;
    transform: rotate(45deg) translateY(20px);
  }
}

@keyframes data-flow-2 {
  0%, 100% {
    opacity: 0;
    transform: rotate(-30deg) translateY(-20px);
  }
  50% {
    opacity: 1;
    transform: rotate(-30deg) translateY(20px);
  }
}

@keyframes data-flow-3 {
  0%, 100% {
    opacity: 0;
    transform: rotate(60deg) translateY(-20px);
  }
  50% {
    opacity: 1;
    transform: rotate(60deg) translateY(20px);
  }
}

@keyframes data-flow-4 {
  0%, 100% {
    opacity: 0;
    transform: rotate(-45deg) translateY(-20px);
  }
  50% {
    opacity: 1;
    transform: rotate(-45deg) translateY(20px);
  }
}

.animate-data-flow-1 {
  animation: data-flow-1 2s ease-in-out infinite;
}

.animate-data-flow-2 {
  animation: data-flow-2 2s ease-in-out infinite 0.5s;
}

.animate-data-flow-3 {
  animation: data-flow-3 2s ease-in-out infinite 1s;
}

.animate-data-flow-4 {
  animation: data-flow-4 2s ease-in-out infinite 1.5s;
}
</style>