<template>
  <div class="feature-section" ref="featureSectionRef">
    <div class="container mx-auto px-4">
      <div class="enhanced-glass-card rounded-3xl overflow-hidden group hover:shadow-2xl transition-all duration-500">
        <!-- 装饰性背景 -->
        <div class="absolute inset-0 opacity-5">
          <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
        </div>
        
        <!-- 渐变装饰 -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-400/20 to-transparent rounded-full blur-2xl group-hover:w-40 group-hover:h-40 transition-all duration-700"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-400/15 to-transparent rounded-full blur-xl group-hover:w-32 group-hover:h-32 transition-all duration-700"></div>
        
        <div class="relative flex flex-col md:flex-row items-center gap-12 p-10 md:p-20" :class="{ 'md:flex-row-reverse': reverse }">
          <!-- Image Section -->
          <div class="w-full md:w-1/2">
            <div class="relative">
              <!-- 图片背景装饰 -->
              <div class="absolute -inset-4 bg-gradient-to-r from-blue-600/10 via-indigo-600/10 to-purple-600/10 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-500"></div>
              
              <div class="relative aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200/50">
                <img 
                  :src="image" 
                  :alt="t(`features.${translationKey}.alt`)"
                  class="w-full h-full object-contain p-6 group-hover:scale-105 transition-transform duration-500"
                  loading="lazy"
                />
                
                <!-- 图片光晕效果 -->
                <div class="absolute inset-0 bg-gradient-to-t from-white/0 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>
          
          <!-- Content Section -->
          <div class="w-full md:w-1/2 space-y-8">
            <!-- 增强的图标容器 -->
            <div class="relative w-fit">
              <!-- 图标背景光晕 -->
              <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl blur-md opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
              
              <div class="relative bg-gradient-to-br from-blue-500 to-indigo-600 p-4 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
                <component :is="icon" class="h-7 w-7 text-white" />
              </div>
              
              <!-- 装饰性圆圈 -->
              <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
            </div>
            
            <!-- 标题 -->
            <h3 class="text-heading-2 md:text-heading-1 font-bold text-gray-900 leading-tight group-hover:text-blue-700 transition-colors duration-300">
              {{ title }}
            </h3>
            
            <!-- 描述 -->
            <p class="text-body-large text-gray-700 leading-relaxed">
              {{ description }}
            </p>
            
            <!-- 新增：特性亮点 -->
            <div class="flex items-center gap-2 text-sm text-blue-600 font-medium opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-500 delay-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right">
                <path d="M5 12h14"/>
                <path d="M12 5l7 7-7 7"/>
              </svg>
              <span>{{ t(`features.${translationKey}.highlight`) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import { ref } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
  icon: Component
  title: string
  description: string
  image: string
  reverse?: boolean
  translationKey: string
}

withDefaults(defineProps<Props>(), {
  reverse: false
})

const featureSectionRef = ref<HTMLElement | null>(null)

// 设置交叉观察器
useIntersectionObserver(featureSectionRef, ([{ isIntersecting }]) => {
  if (isIntersecting && featureSectionRef.value) {
    featureSectionRef.value.classList.add('animate-feature')
  }
})
</script>

<style scoped>
.feature-section {
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  margin: 4rem 0;
  will-change: transform, opacity;
}

.enhanced-glass-card {
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.1),
    0 2px 16px 0 rgba(31, 38, 135, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-glass-card:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 
    0 20px 60px 0 rgba(31, 38, 135, 0.15),
    0 8px 32px 0 rgba(59, 130, 246, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.feature-section.animate-feature {
  animation: enhanced-feature-fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes enhanced-feature-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .feature-section {
    margin: 2rem 0;
  }
  
  .enhanced-glass-card {
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
  }
}
</style> 