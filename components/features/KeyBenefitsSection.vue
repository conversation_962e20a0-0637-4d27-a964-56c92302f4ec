<template>
  <div class="key-benefits-section" ref="sectionRef">
    <div class="container mx-auto px-4 py-20 md:py-26">
      <!-- Section Header -->
      <div class="text-center mb-20 animate-slide-up">
        <h2 class="text-display-3 md:text-display-2 font-bold text-gray-900 mb-6">
          {{ t('keyBenefits.title') }}
        </h2>
        <p class="text-body-large text-gray-700 max-w-3xl mx-auto">
          {{ t('keyBenefits.subtitle') }}
        </p>
      </div>

      <!-- Benefits Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
        <!-- Benefit 1: Security & Privacy -->
        <div
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:400ms]`]"
        >
          <div class="modern-card bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 h-full border border-green-100 hover:border-green-200 transition-all duration-300">
            <!-- Icon -->
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-green-200 transition-colors duration-300">
              <Shield class="w-6 h-6 text-green-600" />
            </div>

            <!-- Content -->
            <h3 class="text-xl font-bold text-gray-900 mb-4">
              {{ t('keyBenefits.security.title') }}
            </h3>

            <p class="text-gray-600 leading-relaxed text-sm">
              {{ t('keyBenefits.security.description') }}
            </p>
          </div>
        </div>

        <!-- Benefit 2: Universal Compatibility -->
        <div
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:600ms]`]"
        >
          <div class="modern-card bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 h-full border border-blue-100 hover:border-blue-200 transition-all duration-300">
            <!-- Icon -->
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-blue-200 transition-colors duration-300">
              <Globe class="w-6 h-6 text-blue-600" />
            </div>

            <!-- Content -->
            <h3 class="text-xl font-bold text-gray-900 mb-4">
              {{ t('keyBenefits.compatibility.title') }}
            </h3>

            <p class="text-gray-600 leading-relaxed text-sm">
              {{ t('keyBenefits.compatibility.description') }}
            </p>
          </div>
        </div>

        <!-- Benefit 3: Multi-language Support -->
        <div
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:800ms]`]"
        >
          <div class="modern-card bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 h-full border border-purple-100 hover:border-purple-200 transition-all duration-300">
            <!-- Icon -->
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-purple-200 transition-colors duration-300">
              <Languages class="w-6 h-6 text-purple-600" />
            </div>

            <!-- Content -->
            <h3 class="text-xl font-bold text-gray-900 mb-4">
              {{ t('keyBenefits.multilingual.title') }}
            </h3>

            <p class="text-gray-600 leading-relaxed text-sm">
              {{ t('keyBenefits.multilingual.description') }}
            </p>
          </div>
        </div>

        <!-- Benefit 4: Time Efficiency -->
        <div
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:1000ms]`]"
        >
          <div class="modern-card bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-8 h-full border border-amber-100 hover:border-amber-200 transition-all duration-300">
            <!-- Icon -->
            <div class="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-amber-200 transition-colors duration-300">
              <Clock class="w-6 h-6 text-amber-600" />
            </div>

            <!-- Content -->
            <h3 class="text-xl font-bold text-gray-900 mb-4">
              Time Efficiency
            </h3>

            <p class="text-gray-600 leading-relaxed text-sm">
              Save hours of manual data entry. Complete forms in seconds instead of minutes with intelligent automation.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Shield, Globe, Languages, Clock } from 'lucide-vue-next'
import { useIntersectionObserver } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const sectionRef = ref<HTMLElement | null>(null)

// 设置交叉观察器
useIntersectionObserver(sectionRef, ([{ isIntersecting }]) => {
  if (isIntersecting && sectionRef.value) {
    sectionRef.value.classList.add('animate-feature')
  }
})
</script>

<style scoped>
.key-benefits-section {
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  will-change: transform, opacity;
}

.benefit-card {
  position: relative;
  transition: all 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-8px);
}

.modern-card {
  position: relative;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.key-benefits-section.animate-feature {
  animation: section-fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes section-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .key-benefits-section {
    margin: 2rem 0;
  }

  .modern-card {
    padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .grid {
    grid-template-columns: 1fr;
  }
}
</style>