<template>
  <div class="key-benefits-section" ref="sectionRef">
    <div class="container mx-auto px-4 py-20 md:py-26">
      <!-- Section Header -->
      <div class="text-center mb-20 animate-slide-up">
        <h2 class="text-display-3 md:text-display-2 font-bold text-gray-900 mb-6">
          {{ t('keyBenefits.title') }}
        </h2>
        <p class="text-body-large text-gray-700 max-w-3xl mx-auto">
          {{ t('keyBenefits.subtitle') }}
        </p>
      </div>

      <!-- Benefits Grid -->
      <div class="grid md:grid-cols-3 gap-10 max-w-7xl mx-auto">
        <!-- Benefit 1: Security & Privacy -->
        <div 
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:400ms]`]"
        >
          <div class="enhanced-glass-card rounded-3xl p-10 h-full">
            <!-- Decorative Elements -->
            <div class="absolute inset-0 opacity-5">
              <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(16, 185, 129, 0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
            </div>
            
            <div class="relative text-center h-full flex flex-col">
              <!-- Icon Container -->
              <div class="relative w-fit mx-auto mb-8">
                <div class="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl blur-md opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
                <div class="relative bg-gradient-to-br from-green-500 to-emerald-600 p-5 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
                  <Shield class="w-8 h-8 text-white" />
                </div>
              </div>

              <!-- Content -->
              <h3 class="text-heading-2 font-bold text-gray-900 mb-6 group-hover:text-green-700 transition-colors">
                {{ t('keyBenefits.security.title') }}
              </h3>
              
              <p class="text-body-base text-gray-700 leading-relaxed mb-8 flex-grow">
                {{ t('keyBenefits.security.description') }}
              </p>

              <!-- Feature Highlights -->
              <div class="space-y-4">
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-green-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.security.features.ollama.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.security.features.ollama.description') }}</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-green-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.security.features.privacy.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.security.features.privacy.description') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Benefit 2: Universal Compatibility -->
        <div 
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:600ms]`]"
        >
          <div class="enhanced-glass-card rounded-3xl p-10 h-full">
            <!-- Decorative Elements -->
            <div class="absolute inset-0 opacity-5">
              <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
            </div>
            
            <div class="relative text-center h-full flex flex-col">
              <!-- Icon Container -->
              <div class="relative w-fit mx-auto mb-8">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl blur-md opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
                <div class="relative bg-gradient-to-br from-blue-500 to-indigo-600 p-5 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
                  <Globe class="w-8 h-8 text-white" />
                </div>
              </div>

              <!-- Content -->
              <h3 class="text-heading-2 font-bold text-gray-900 mb-6 group-hover:text-blue-700 transition-colors">
                {{ t('keyBenefits.compatibility.title') }}
              </h3>
              
              <p class="text-body-base text-gray-700 leading-relaxed mb-8 flex-grow">
                {{ t('keyBenefits.compatibility.description') }}
              </p>

              <!-- Feature Highlights -->
              <div class="space-y-4">
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-blue-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.compatibility.features.websites.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.compatibility.features.websites.description') }}</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-blue-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.compatibility.features.forms.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.compatibility.features.forms.description') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Benefit 3: Multi-language Support -->
        <div 
          class="benefit-card group"
          :class="[`animate-slide-up [animation-delay:800ms]`]"
        >
          <div class="enhanced-glass-card rounded-3xl p-10 h-full">
            <!-- Decorative Elements -->
            <div class="absolute inset-0 opacity-5">
              <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(168, 85, 247, 0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
            </div>
            
            <div class="relative text-center h-full flex flex-col">
              <!-- Icon Container -->
              <div class="relative w-fit mx-auto mb-8">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl blur-md opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
                <div class="relative bg-gradient-to-br from-purple-500 to-pink-600 p-5 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
                  <Languages class="w-8 h-8 text-white" />
                </div>
              </div>

              <!-- Content -->
              <h3 class="text-heading-2 font-bold text-gray-900 mb-6 group-hover:text-purple-700 transition-colors">
                {{ t('keyBenefits.multilingual.title') }}
              </h3>
              
              <p class="text-body-base text-gray-700 leading-relaxed mb-8 flex-grow">
                {{ t('keyBenefits.multilingual.description') }}
              </p>

              <!-- Feature Highlights -->
              <div class="space-y-4">
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-purple-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.multilingual.features.detection.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.multilingual.features.detection.description') }}</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 text-left">
                  <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check class="w-3 h-3 text-purple-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 mb-1">{{ t('keyBenefits.multilingual.features.choice.title') }}</h4>
                    <p class="text-sm text-gray-600">{{ t('keyBenefits.multilingual.features.choice.description') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Shield, Globe, Languages, Check } from 'lucide-vue-next'
import { useIntersectionObserver } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const sectionRef = ref<HTMLElement | null>(null)

// 设置交叉观察器
useIntersectionObserver(sectionRef, ([{ isIntersecting }]) => {
  if (isIntersecting && sectionRef.value) {
    sectionRef.value.classList.add('animate-feature')
  }
})
</script>

<style scoped>
.key-benefits-section {
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  will-change: transform, opacity;
}

.benefit-card {
  position: relative;
  transition: all 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-4px);
}

.enhanced-glass-card {
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.1),
    0 2px 16px 0 rgba(31, 38, 135, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-glass-card:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 
    0 20px 60px 0 rgba(31, 38, 135, 0.15),
    0 8px 32px 0 rgba(59, 130, 246, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.key-benefits-section.animate-feature {
  animation: section-fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes section-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .key-benefits-section {
    margin: 2rem 0;
  }
  
  .enhanced-glass-card {
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
  }
}
</style>