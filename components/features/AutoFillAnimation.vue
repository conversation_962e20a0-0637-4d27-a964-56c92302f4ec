<template>
  <div class="auto-fill-container">
    <!-- 标题和描述 -->
    <div class="text-center mb-12">
      <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        {{ t('howItWorks.steps.autoFill.title') }}
      </h3>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {{ t('howItWorks.steps.autoFill.description') }}
      </p>
    </div>

    <!-- 表单选择器 -->
    <div class="flex justify-center mb-8">
      <div class="inline-flex p-1 bg-gray-100 rounded-xl">
        <button
          v-for="(form, index) in forms"
          :key="form.id"
          @click="activeForm = index"
          class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200"
          :class="{
            'bg-white text-gray-900 shadow-sm': activeForm === index,
            'text-gray-600 hover:text-gray-900': activeForm !== index
          }"
        >
          {{ form.name }}
        </button>
      </div>
    </div>

    <!-- 表单动画区域 -->
    <div class="relative max-w-4xl mx-auto">
      <Transition name="form-switch" mode="out-in">
        <div :key="activeForm" class="form-animation-wrapper">
          <!-- 通用表单 -->
          <div v-if="activeForm === 0" class="form-container bg-white rounded-xl shadow-lg border p-8">
            <h4 class="text-lg font-semibold text-gray-800 mb-6">{{ t('demo.forms.userRegistration') }}</h4>
            <div class="grid md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <FormField 
                  label="Username" 
                  :value="fillValues.general.username" 
                  :delay="0.5"
                  placeholder="Enter your username"
                />
                <FormField 
                  label="Email" 
                  :value="fillValues.general.email" 
                  :delay="1"
                  placeholder="Enter your email address"
                />
                <FormField 
                  label="Password" 
                  :value="fillValues.general.password" 
                  :delay="1.5"
                  placeholder="Enter your password"
                  type="password"
                />
              </div>
              <div class="space-y-4">
                <FormField 
                  label="Phone Number" 
                  :value="fillValues.general.phone" 
                  :delay="2"
                  placeholder="Enter your phone number"
                />
                <FormField 
                  label="Company" 
                  :value="fillValues.general.company" 
                  :delay="2.5"
                  placeholder="Enter your company name"
                />
                <FormField 
                  label="Position" 
                  :value="fillValues.general.position" 
                  :delay="3"
                  placeholder="Enter your position"
                />
              </div>
            </div>
          </div>

          <!-- 邮件表单 -->
          <div v-else-if="activeForm === 1" class="form-container bg-white rounded-xl shadow-lg border p-8">
            <h4 class="text-lg font-semibold text-gray-800 mb-6">{{ t('demo.forms.sendEmail') }}</h4>
            <div class="space-y-4">
              <!-- To 和 CC 在同一行 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <label class="block text-sm font-medium text-gray-700 mr-2 whitespace-nowrap">{{ t('demo.email.to') }}:</label>
                  <div class="flex-grow">
                    <FormField 
                      :label="''"
                      :value="fillValues.email.to" 
                      :delay="0.5"
                      :placeholder="t('demo.email.toPlaceholder')"
                    />
                  </div>
                </div>
                <div class="flex items-center">
                  <label class="block text-sm font-medium text-gray-700 mr-2 whitespace-nowrap">{{ t('demo.email.cc') }}:</label>
                  <div class="flex-grow">
                    <FormField 
                      :label="''"
                      :value="fillValues.email.cc" 
                      :delay="1"
                      :placeholder="t('demo.email.ccPlaceholder')"
                    />
                  </div>
                </div>
              </div>
              
              <!-- Subject 标题和文本框在一行 -->
              <div class="flex items-center">
                <label class="block text-sm font-medium text-gray-700 mr-2 whitespace-nowrap">{{ t('demo.email.subject') }}:</label>
                <div class="flex-grow">
                  <FormField 
                    :label="''"
                    :value="fillValues.email.subject" 
                    :delay="1.5"
                    :placeholder="t('demo.email.subjectPlaceholder')"
                  />
                </div>
              </div>
              
              <!-- Body 不显示标题 -->
              <div>
                <FormField 
                  label=""
                  :value="fillValues.email.body" 
                  :delay="2"
                  :placeholder="t('demo.email.bodyPlaceholder')"
                  :is-textarea="true"
                  :rows="8"
                />
              </div>
            </div>
          </div>

          <!-- Bug 报告表单 -->
          <div v-else-if="activeForm === 2" class="form-container bg-white rounded-xl shadow-lg border p-8">
            <h4 class="text-lg font-semibold text-gray-800 mb-6">{{ t('demo.forms.bugReport') }}</h4>
            <div class="space-y-4">
              <FormField 
                label="Bug Title" 
                :value="fillValues.bug.title" 
                :delay="0.5"
                placeholder="Briefly describe the bug"
              />
              <div class="grid md:grid-cols-2 gap-4">
                <FormField 
                  label="Severity" 
                  :value="fillValues.bug.severity" 
                  :delay="1"
                  placeholder="Select severity level"
                />
                <FormField 
                  label="Priority" 
                  :value="fillValues.bug.priority" 
                  :delay="1.2"
                  placeholder="Select priority level"
                />
              </div>
              <FormField 
                label="Reproduction Steps" 
                :value="fillValues.bug.steps" 
                :delay="1.5"
                placeholder="Describe how to reproduce this bug"
                :is-textarea="true"
              />
              <FormField 
                label="Expected Result" 
                :value="fillValues.bug.expected" 
                :delay="2"
                placeholder="Describe the expected correct behavior"
                :is-textarea="true"
              />
              <FormField 
                label="Actual Result" 
                :value="fillValues.bug.actual" 
                :delay="2.5"
                placeholder="Describe what actually happened"
                :is-textarea="true"
              />
            </div>
          </div>
        </div>
      </Transition>

    </div>

    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import FormField from './FormField.vue'

const { t } = useI18n()
const activeForm = ref(0)

const forms = [
  { id: 'general', name: t('demo.forms.general') },
  { id: 'email', name: t('demo.forms.email') },
  { id: 'bug', name: t('demo.forms.bug') }
]

const fillValues = {
  general: {
    username: 'john_doe',
    email: '<EMAIL>',
    password: '••••••••',
    phone: '+****************',
    company: 'TechCorp Inc.',
    position: 'Frontend Developer'
  },
  email: {
    to: '<EMAIL>',
    cc: '<EMAIL>',
    subject: 'Project Status Update',
    body: 'Hi team,\n\nI wanted to share an update on this week\'s project progress. We\'ve completed the main UI functionality development and are now in the testing phase.\n\nLooking forward to your feedback!\n\nBest regards,\nJohn'
  },
  bug: {
    title: 'Login Page Not Displaying Correctly in Safari',
    severity: 'High',
    priority: 'Urgent',
    steps: '1. Open Safari browser\n2. Navigate to the login page\n3. Observe the page layout',
    expected: 'The page should display correctly with all elements properly aligned',
    actual: 'The page layout is broken, with buttons in wrong positions and overlapping text'
  }
}


</script>

<style scoped>
.form-switch-enter-active,
.form-switch-leave-active {
  transition: all 0.3s ease;
}

.form-switch-enter-from,
.form-switch-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.form-animation-wrapper {
  min-height: 400px;
}
</style>