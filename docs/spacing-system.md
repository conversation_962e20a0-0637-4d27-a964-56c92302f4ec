# Fillify Web 间距系统规范

## 概述
本项目采用基于 8pt 网格的标准化间距系统，确保设计的一致性和可维护性。

## 8pt 网格系统

### 核心原则
- 所有间距值基于 8pt (8px) 的倍数
- 使用 Tailwind CSS 自定义间距配置
- 确保在不同设备尺寸下的一致性

### 间距映射表

#### 小间距 (小于 200px)
- `18` = 4.5rem = 72px (9 × 8pt)
- `22` = 5.5rem = 88px (11 × 8pt)
- `26` = 6.5rem = 104px (13 × 8pt)
- `30` = 7.5rem = 120px (15 × 8pt)
- `34` = 8.5rem = 136px (17 × 8pt)
- `38` = 9.5rem = 152px (19 × 8pt)

#### 中等间距 (200px - 300px)
- `42` = 10.5rem = 168px (21 × 8pt)
- `46` = 11.5rem = 184px (23 × 8pt)
- `50` = 12.5rem = 200px (25 × 8pt)
- `54` = 13.5rem = 216px (27 × 8pt)
- `58` = 14.5rem = 232px (29 × 8pt)
- `62` = 15.5rem = 248px (31 × 8pt)

#### 大间距 (大于 300px)
- `66` = 16.5rem = 264px (33 × 8pt)
- `70` = 17.5rem = 280px (35 × 8pt)
- `74` = 18.5rem = 296px (37 × 8pt)
- `78` = 19.5rem = 312px (39 × 8pt)
- `82` = 20.5rem = 328px (41 × 8pt)
- `86` = 21.5rem = 344px (43 × 8pt)
- `90` = 22.5rem = 360px (45 × 8pt)
- `94` = 23.5rem = 376px (47 × 8pt)
- `98` = 24.5rem = 392px (49 × 8pt)

## 使用指南

### 页面级间距
- **Hero 区域**: `py-20 md:py-26`
- **内容区域**: `py-20 md:py-26`
- **底部区域**: `py-20 md:py-30`

### 组件级间距
- **卡片内边距**: `p-10 md:p-16`
- **元素间距**: `gap-8` 或 `gap-10`
- **文本间距**: `space-y-8`

### 响应式间距
```css
/* 移动端优先的间距规范 */
py-20        /* 基础间距 (移动端) */
md:py-26     /* 中等屏幕间距 */
lg:py-30     /* 大屏幕间距 */
```

## 字体系统

### 显示级字体
- `display-1`: 4.5rem (72px) - 主标题
- `display-2`: 3.75rem (60px) - 副标题
- `display-3`: 3rem (48px) - 重要标题

### 标题级字体
- `heading-1`: 2.25rem (36px) - H1
- `heading-2`: 1.875rem (30px) - H2
- `heading-3`: 1.5rem (24px) - H3

### 正文字体
- `body-large`: 1.125rem (18px) - 大正文
- `body-base`: 1rem (16px) - 基础正文
- `body-small`: 0.875rem (14px) - 小正文
- `caption`: 0.75rem (12px) - 说明文字

## 实施检查清单

### ✅ 已完成
- [x] Tailwind 配置更新
- [x] 首页间距统一
- [x] 博客页面间距统一
- [x] FeatureSection 组件间距统一
- [x] 头部和尾部组件间距统一
- [x] 验证语法错误

### 📋 组件间距标准
- **Header**: 高度 `h-16` (64px = 8 × 8pt)
- **Footer**: 内边距 `py-10` (80px = 10 × 8pt)
- **Feature Section**: 内边距 `p-10 md:p-16`
- **页面容器**: 间距 `py-20 md:py-26`

## 注意事项

1. **保持一致性**: 始终使用定义的间距值
2. **响应式设计**: 确保在不同屏幕尺寸下的适配
3. **可维护性**: 避免使用任意值，坚持使用系统定义的间距
4. **测试验证**: 每次修改后验证视觉效果和响应式表现

## 更新日志
- 2024-XX-XX: 建立初始 8pt 网格间距系统
- 应用到所有主要页面和组件
- 创建标准化字体系统