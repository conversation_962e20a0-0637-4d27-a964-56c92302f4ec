/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './components/**/*.{js,vue,ts}',
    './layouts/**/*.vue',
    './pages/**/*.vue',
    './plugins/**/*.{js,ts}',
    './nuxt.config.{js,ts}',
    './app.vue',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // 8pt Grid System - 标准化间距系统
      spacing: {
        // 小间距 (8pt基础)
        '18': '4.5rem',   // 72px (9 * 8pt)
        '22': '5.5rem',   // 88px (11 * 8pt)
        '26': '6.5rem',   // 104px (13 * 8pt)
        '30': '7.5rem',   // 120px (15 * 8pt)
        '34': '8.5rem',   // 136px (17 * 8pt)
        '38': '9.5rem',   // 152px (19 * 8pt)
        // 中等间距
        '42': '10.5rem',  // 168px (21 * 8pt)
        '46': '11.5rem',  // 184px (23 * 8pt)
        '50': '12.5rem',  // 200px (25 * 8pt)
        '54': '13.5rem',  // 216px (27 * 8pt)
        '58': '14.5rem',  // 232px (29 * 8pt)
        '62': '15.5rem',  // 248px (31 * 8pt)
        // 大间距
        '66': '16.5rem',  // 264px (33 * 8pt)
        '70': '17.5rem',  // 280px (35 * 8pt)
        '74': '18.5rem',  // 296px (37 * 8pt)
        '78': '19.5rem',  // 312px (39 * 8pt)
        '82': '20.5rem',  // 328px (41 * 8pt)
        '86': '21.5rem',  // 344px (43 * 8pt)
        '90': '22.5rem',  // 360px (45 * 8pt)
        '94': '23.5rem',  // 376px (47 * 8pt)
        '98': '24.5rem',  // 392px (49 * 8pt)
      },
      // 标准化字体大小（基于模块化比例）
      fontSize: {
        'display-1': ['4.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }], // 72px
        'display-2': ['3.75rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }], // 60px
        'display-3': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.02em' }], // 48px
        'heading-1': ['2.25rem', { lineHeight: '1.2', letterSpacing: '-0.02em' }], // 36px
        'heading-2': ['1.875rem', { lineHeight: '1.3', letterSpacing: '-0.01em' }], // 30px
        'heading-3': ['1.5rem', { lineHeight: '1.3', letterSpacing: '-0.01em' }], // 24px
        'body-large': ['1.125rem', { lineHeight: '1.6' }], // 18px
        'body-base': ['1rem', { lineHeight: '1.6' }], // 16px
        'body-small': ['0.875rem', { lineHeight: '1.5' }], // 14px
        'caption': ['0.75rem', { lineHeight: '1.4' }], // 12px
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        fadeIn: {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fadeIn 0.8s ease-out forwards",
      },
      backdropBlur: {
        'xl': '24px',
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
}
