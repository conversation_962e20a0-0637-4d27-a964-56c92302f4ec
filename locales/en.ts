export default {
  $locale: {
    name: 'English',
    nativeName: 'English'
  },
  welcome: 'Welcome to Fillify',
  description: 'AI-Powered Form Filling',
  nav: {
    home: 'Home',
    blog: 'Blog',
    signin: 'Sign in',
    dashboard: 'Dashboard',
    signout: 'Sign out',
    startFree: 'Start Free',
    language: 'Language'
  },
  hero: {
    chromeStore: 'Now Available on Chrome Web Store',
    title: {
      text: 'Transform Your Forms Filling with ',
      rotatingWords: {
        0: 'AI-Powered Magic',
        1: 'Smart Automation',
        2: 'Future Technology',
        3: 'Perfect Precision',
        4: 'Seamless Integration'
      }
    },
    description: 'Just type one sentence, and AI will instantly fill any web form. The smartest way to handle online forms.',
    cta: {
      chrome: 'Add to Chrome',
      learnMore: 'Learn More'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: 'Forms Filled Daily'
    },
    accuracy: {
      value: '%',
      label: 'Accuracy Rate'
    },
    support: {
      value: '24/7',
      label: 'AI Support'
    }
  },
  howItWorks: {
    title: 'How Fillify Works',
    subtitle: 'Experience the seamless three-step process that transforms your form filling experience',
    steps: {
      detection: {
        title: 'Smart Detection',
        description: '<PERSON>llify automatically identifies and analyzes form fields on any webpage, understanding their purpose and requirements.',
        features: [
          'Intelligent field recognition',
          'Context-aware analysis'
        ]
      },
      analysis: {
        title: 'AI Analysis', 
        description: 'Our advanced AI processes your input description and generates accurate, contextually relevant content tailored to each field.',
        features: [
          'Natural language understanding',
          'Context-aware generation'
        ]
      },
      autoFill: {
        title: 'Auto-Fill',
        description: 'The generated content is instantly and precisely filled into the appropriate form fields, saving you time and effort.',
        features: [
          'One-click completion',
          'Precision mapping'
        ]
      }
    }
  },
  keyBenefits: {
    title: 'Why Choose Fillify',
    subtitle: 'Discover the key advantages that make Fillify the ultimate form filling solution',
    security: {
      title: 'Privacy & Security First',
      description: 'Your data privacy is our top priority. Run AI models locally or use secure cloud providers with complete control over your information.',
      features: {
        ollama: {
          title: 'Local AI with Ollama',
          description: 'Run AI models entirely on your device for maximum privacy'
        },
        privacy: {
          title: 'Zero Data Storage',
          description: 'We never store your personal information or form data'
        }
      }
    },
    compatibility: {
      title: 'Universal Compatibility',
      description: 'Works seamlessly across all websites and form types. From simple contact forms to complex applications, Fillify handles them all.',
      features: {
        websites: {
          title: 'Any Website',
          description: 'Compatible with all major websites and platforms'
        },
        forms: {
          title: 'All Form Types',
          description: 'Handles registration, applications, surveys, and more'
        }
      }
    },
    multilingual: {
      title: 'Global Language Support',
      description: 'Generate content in multiple languages with intelligent language detection and flexible output language selection.',
      features: {
        detection: {
          title: 'Smart Detection',
          description: 'Automatically detects form language and context'
        },
        choice: {
          title: 'Language Freedom',
          description: 'Choose your preferred output language for any form'
        }
      }
    }
  },
  features: {
    title: 'Experience the Power of AI',
    subtitle: 'Discover how Fillify transforms your daily workflow with intelligent automation',
    formFilling: {
      title: 'Universal Form Filling',
      description: 'Fill any web form with AI precision. From simple registration forms to complex applications, Fillify transforms your text descriptions into accurate form data, saving you hours of manual work.',
      highlight: 'Works with any website',
      alt: 'Screenshot showing Fillify automatically filling a web form with AI assistance'
    },
    email: {
      title: 'Smart Email Assistant',
      description: 'Instantly compose professional emails with our AI-powered assistant. Supporting Gmail and Outlook, it converts your brief descriptions into well-structured emails, making email writing effortless.',
      highlight: 'Gmail & Outlook support',
      alt: 'Demonstration of Fillify composing an email with AI suggestions'
    },
    bugReport: {
      title: 'Intelligent Bug Reports',
      description: 'Generate comprehensive bug reports with a single click. Supporting GitHub Issues, JIRA, and other platforms, our AI transforms your brief descriptions into detailed, well-structured bug reports that help your team communicate effectively.',
      highlight: 'GitHub & JIRA ready',
      alt: 'Example of Fillify generating a detailed bug report for GitHub Issues'
    },
    aiProvider: {
      title: 'Choose Your AI Provider',
      description: 'Freedom to choose your preferred AI service. Whether it\'s OpenAI, Anthropic Claude, or others - simply plug in your API key and start using your favorite AI model. Full control over your AI experience with easy provider switching.',
      highlight: 'Multiple AI providers',
      alt: 'Interface showing different AI provider options in Fillify'
    }
  },
  faq: {
    title: 'Frequently Asked Questions',
    items: {
      what: {
        question: 'What is Fillify?',
        answer: 'Fillify is an AI-powered browser extension that helps you fill out forms, compose emails, and create detailed bug reports with a single click by leveraging advanced AI models to understand your needs and automatically complete fields, saving you time and effort on repetitive tasks.'
      },
      types: {
        question: 'What types of forms can Fillify handle?',
        answer: 'Fillify works with various forms, including general web forms, bug reports, and emails. It supports text fields, text areas, and more, ensuring seamless automation across different websites.'
      },
      providers: {
        question: 'Which AI providers does Fillify support?',
        answer: 'Fillify supports multiple leading AI providers such as OpenAI’s GPT models, Anthropic’s Claude series, Google’s Gemini models, DeepSeek, Moonshot, and even local AI models through Ollama, giving you maximum flexibility and choice over how content is generated.'
      },
      privacy: {
        question: 'How does Fillify protect my data and privacy?',
        answer: "Fillify never sends your API keys or form data to our servers—your keys are stored securely in your browser and all requests go directly from your device to the AI provider. This means only you and the chosen provider ever see your data. For even stronger control, Fillify supports Ollama so you can run AI models entirely on your own machine, ensuring maximum security and privacy."
      },
      customize: {
        question: 'Can I customize AI responses for specific forms?',
        answer: 'Yes! In bug report mode, you can create custom templates with predefined information to help generate more accurate and consistent bug reports.'
      },
      languages: {
        question: 'What languages does Fillify support?',
        answer: 'Fillify supports multiple languages and can automatically detect the form\'s language. You can also manually select your preferred output language in the extension popup.'
      }
    }
  },
  demo: {
    contact: {
      title: 'Contact Us',
      name: 'Name',
      namePlaceholder: 'Please enter your name',
      email: 'Email',
      emailPlaceholder: 'Please enter your email',
      message: 'Message',
      messagePlaceholder: 'Please enter your message',
      submit: 'Submit'
    },
    forms: {
      general: 'General Form',
      email: 'Email Form', 
      bug: 'Bug Report',
      userRegistration: 'User Registration Form',
      sendEmail: 'Send Email',
      bugReport: 'Bug Report'
    },
    email: {
      to: 'To',
      toPlaceholder: 'Enter recipient email',
      cc: 'CC',
      ccPlaceholder: 'Enter CC email',
      subject: 'Subject',
      subjectPlaceholder: 'Enter email subject',
      body: 'Body',
      bodyPlaceholder: 'Enter email content'
    },
    status: {
      detected: 'Detected {count} form fields',
      analyzing: 'AI is analyzing form content...',
      filling: 'Filling form ({current}/{total})'
    },
    tags: {
      smartDetection: 'Smart Detection',
      autoDetection: 'Auto Detection', 
      formFields: 'Form Fields',
      aiAnalysis: 'AI Analysis',
      multiModel: 'Multi-Model',
      smartUnderstanding: 'Smart Understanding',
      autoFill: 'Auto-Fill',
      quickComplete: 'Quick Complete',
      improveEfficiency: 'Improve Efficiency'
    },
    meta: {
      title: 'Demo Form - Test Fillify Auto-Fill',
      description: 'Try out Fillify\'s AI-powered form filling capabilities with this interactive demo form. See how AI can transform your form filling experience.'
    }
  },
  bottomCta: {
    subtitle: 'Ready to Transform Your Workflow?',
    title: 'Experience the Future of Form Filling Today',
    button: 'Install Now'
  },
  footer: {
    copyright: '© {year} Fillify. All rights reserved.',
    social: {
      twitter: 'X (Twitter)',
      youtube: 'YouTube'
    },
    links: {
      terms: 'Terms of Service',
      privacy: 'Privacy Policy'
    }
  },
  signin: {
    title: 'Welcome to Fillify',
    subtitle: 'Sign in to use our AI-powered form filling extension',
    features: {
      title: 'What you\'ll get:',
      list: {
        autoFill: 'AI-Powered Form Auto-Fill',
        api: 'Customize with Your Own API',
        early: 'Early Access to New Features'
      }
    },
    terms: {
      prefix: 'By signing in, you agree to our',
      and: 'and'
    },
    seo: {
      title: 'Sign in - Fillify',
      description: 'Sign in to Fillify to access AI-powered form filling features'
    }
  },
  meta: {
    title: 'Fillify - AI-Powered Forms, Emails & Bug Reports Assistant',
    description: 'Fillify revolutionizes form filling with AI technology. Automatically complete web forms, compose emails, and generate bug reports with intelligent automation.',
    keywords: {
      formFilling: 'AI Form Filler',
      automation: 'AI Automation',
      email: 'AI Email Generation',
      bugReport: 'AI Bug Report Generation',
      additional: [
        'Smart Form Completion',
        'Automated Data Entry',
        'AI Form Assistant',
        'Intelligent Form Filling',
        'Chrome Form Autofill',
        'AI Form Filler'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'Privacy Policy - Fillify',
      description: 'Learn about how Fillify protects your privacy and handles your data.'
    },
    title: 'Privacy Policy',
    lastUpdated: 'Last updated: {date}'
  },
  terms: {
    meta: {
      title: 'Terms of Service - Fillify',
      description: 'Read about the terms and conditions for using Fillify services.'
    },
    title: 'Terms of Service',
    lastUpdated: 'Last updated: {date}'
  },
  dashboard: {
    meta: {
      title: 'Dashboard - Fillify',
      description: 'Manage your Fillify account, view your current plan, and track usage.'
    },
    currentPlan: 'Current Plan',
    settings: 'Settings',
    usageOverview: 'Usage Overview',
    creditsUsed: 'Credits Used'
  },
  blog: {
    meta: {
      title: 'Blog - Fillify',
      description: 'Read the latest news, updates, and tips about AI-powered form filling and productivity automation.'
    },
    hero: {
      badge: 'Latest Updates',
      title: 'Blog',
      subtitle: 'Latest news, releases, and tips'
    },
    list: {
      readMore: 'Read More',
      publishedOn: 'Published on',
      minRead: 'min read',
      noPostsTitle: 'No posts yet',
      noPostsDescription: 'We\'re working on creating great content. Please check back soon.'
    },
    article: {
      backToBlog: 'Back to Blog',
      thanksTitle: 'Thanks for reading!',
      thanksDescription: 'If you have any questions or suggestions about Fillify, feel free to contact us.',
      tryFillify: 'Try Fillify',
      moreArticles: 'More Articles',
      notFoundTitle: 'Article Not Found',
      notFoundDescription: 'Sorry, the article you\'re looking for doesn\'t exist or has been removed.',
      backToBlogBtn: 'Back to Blog'
    }
  },
  '404': {
    title: 'Page Not Found',
    description: 'Sorry, we couldn\'t find the page you\'re looking for. Please check the URL or return to the homepage.',
    backHome: 'Back to Home'
  }
} 